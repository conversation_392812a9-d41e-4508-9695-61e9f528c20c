/**
 * Trading Configuration Service
 * Manages trading modes, auto-trade settings, and strategy parameters
 */

import { supabase } from '@/lib/supabase';

export interface TradingConfiguration {
  id?: string;
  userId: string;
  autoTradeEnabled: boolean;
  tradingMode: 'SANDBOX' | 'LIVE';
  strategyName: string;
  strategyParams: {
    emaLength: number;
    lookbackPeriod: number;
    defaultQuantity: number;
    maxTradesPerDay: number;
    riskManagement: {
      maxLossPerTrade: number;
      maxDailyLoss: number;
      stopLossPercentage: number;
      takeProfitPercentage: number;
    };
  };
  subscribedInstruments: Array<{
    symbol: string;
    name: string;
    exchange: string;
    securityId: number;
    enabled: boolean;
  }>;
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  webhookUrl?: string;
  lastStrategyRun?: Date;
  dailyTradeCount: number;
  dailyPnl: number;
  lastDailyReset: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TradingStats {
  dailyTradeCount: number;
  dailyPnl: number;
  maxTradesPerDay: number;
  canTrade: boolean;
  remainingTrades: number;
}

export class TradingConfigService {
  /**
   * Get user's trading configuration
   */
  async getTradingConfig(userId: string): Promise<TradingConfiguration | null> {
    try {
      console.log('Getting trading config for user:', userId);

      // Try using the database function first for better RLS handling
      const { data: functionResult, error: functionError } = await supabase
        .rpc('initialize_user_trading_config', { target_user_id: userId });

      if (!functionError && functionResult) {
        console.log('Got config from function:', functionResult);
        return this.mapDbConfigToTradingConfig(functionResult);
      }

      console.log('Function approach failed, trying direct query. Error:', functionError);

      // Fallback to direct query
      const { data, error } = await supabase
        .from('trading_configuration')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching trading config:', error);
        if (error.code === 'PGRST116') {
          // No configuration found, create default
          console.log('No configuration found, creating default for user:', userId);
          return await this.createDefaultConfig(userId);
        }
        // For RLS policy violations or other errors, try to create default config
        if (error.code === '42501' || error.message?.includes('row-level security')) {
          console.log('RLS policy issue, attempting to create default config for user:', userId);
          return await this.createDefaultConfig(userId);
        }
        return null;
      }

      return this.mapDbConfigToTradingConfig(data);
    } catch (error) {
      console.error('Error in getTradingConfig:', error);
      // Try to create default config as fallback
      try {
        return await this.createDefaultConfig(userId);
      } catch (createError) {
        console.error('Failed to create default config:', createError);
        return null;
      }
    }
  }

  /**
   * Update trading configuration
   */
  async updateTradingConfig(userId: string, updates: Partial<TradingConfiguration>): Promise<boolean> {
    try {
      const updateData: any = {};

      if (updates.autoTradeEnabled !== undefined) {
        updateData.auto_trade_enabled = updates.autoTradeEnabled;
      }
      if (updates.tradingMode !== undefined) {
        updateData.trading_mode = updates.tradingMode;
      }
      if (updates.strategyName !== undefined) {
        updateData.strategy_name = updates.strategyName;
      }
      if (updates.strategyParams !== undefined) {
        updateData.strategy_params = updates.strategyParams;
      }
      if (updates.subscribedInstruments !== undefined) {
        updateData.subscribed_instruments = updates.subscribedInstruments;
      }
      if (updates.notificationsEnabled !== undefined) {
        updateData.notifications_enabled = updates.notificationsEnabled;
      }
      if (updates.emailNotifications !== undefined) {
        updateData.email_notifications = updates.emailNotifications;
      }
      if (updates.webhookUrl !== undefined) {
        updateData.webhook_url = updates.webhookUrl;
      }

      const { error } = await supabase
        .from('trading_configuration')
        .update(updateData)
        .eq('user_id', userId);

      if (error) {
        console.error('Error updating trading config:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateTradingConfig:', error);
      return false;
    }
  }

  /**
   * Toggle auto-trade setting
   */
  async toggleAutoTrade(userId: string): Promise<boolean> {
    try {
      const config = await this.getTradingConfig(userId);
      if (!config) return false;

      return await this.updateTradingConfig(userId, {
        autoTradeEnabled: !config.autoTradeEnabled
      });
    } catch (error) {
      console.error('Error toggling auto-trade:', error);
      return false;
    }
  }

  /**
   * Switch trading mode between SANDBOX and LIVE
   */
  async switchTradingMode(userId: string, mode: 'SANDBOX' | 'LIVE'): Promise<boolean> {
    try {
      return await this.updateTradingConfig(userId, {
        tradingMode: mode
      });
    } catch (error) {
      console.error('Error switching trading mode:', error);
      return false;
    }
  }

  /**
   * Get trading statistics
   */
  async getTradingStats(userId: string): Promise<TradingStats | null> {
    try {
      const config = await this.getTradingConfig(userId);
      if (!config) return null;

      // Reset daily counters if needed
      await this.resetDailyCountersIfNeeded(userId);

      const maxTrades = config.strategyParams.maxTradesPerDay;
      const remainingTrades = Math.max(0, maxTrades - config.dailyTradeCount);

      return {
        dailyTradeCount: config.dailyTradeCount,
        dailyPnl: config.dailyPnl,
        maxTradesPerDay: maxTrades,
        canTrade: remainingTrades > 0 && config.autoTradeEnabled,
        remainingTrades
      };
    } catch (error) {
      console.error('Error getting trading stats:', error);
      return null;
    }
  }

  /**
   * Increment daily trade count
   */
  async incrementTradeCount(userId: string, pnl: number = 0): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('trading_configuration')
        .rpc('increment_trade_stats', {
          user_id_param: userId,
          pnl_param: pnl
        })
        .eq('user_id', userId);

      if (error) {
        console.error('Error incrementing trade count:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in incrementTradeCount:', error);
      return false;
    }
  }

  /**
   * Reset daily counters if needed
   */
  async resetDailyCountersIfNeeded(userId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('reset_daily_trading_counters');
      
      if (error) {
        console.error('Error resetting daily counters:', error);
      }
    } catch (error) {
      console.error('Error in resetDailyCountersIfNeeded:', error);
    }
  }

  /**
   * Create default configuration for new user
   */
  private async createDefaultConfig(userId: string): Promise<TradingConfiguration | null> {
    try {
      console.log('Creating default config for user:', userId);

      const defaultConfig = {
        user_id: userId,
        auto_trade_enabled: false,
        trading_mode: 'SANDBOX' as const,
        strategy_name: 'EMA Scalper',
        strategy_params: {
          emaLength: 20,
          lookbackPeriod: 8,
          defaultQuantity: 1,
          maxTradesPerDay: 10,
          riskManagement: {
            maxLossPerTrade: 500,
            maxDailyLoss: 2000,
            stopLossPercentage: 2.0,
            takeProfitPercentage: 3.0
          }
        },
        subscribed_instruments: [
          {
            symbol: 'NIFTY',
            name: 'NIFTY 50',
            exchange: 'NSE',
            securityId: 13,
            enabled: true
          },
          {
            symbol: 'BANKNIFTY',
            name: 'BANK NIFTY',
            exchange: 'NSE',
            securityId: 25,
            enabled: true
          }
        ],
        notifications_enabled: true,
        email_notifications: false,
        daily_trade_count: 0,
        daily_pnl: 0.0,
        max_trades_per_day: 10
      };

      // Use upsert to handle conflicts
      const { data, error } = await supabase
        .from('trading_configuration')
        .upsert(defaultConfig, {
          onConflict: 'user_id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating default config:', error);
        // If still failing, try a direct insert with error handling
        const { data: insertData, error: insertError } = await supabase
          .from('trading_configuration')
          .insert(defaultConfig)
          .select()
          .single();

        if (insertError) {
          console.error('Error with direct insert:', insertError);
          return null;
        }

        return this.mapDbConfigToTradingConfig(insertData);
      }

      console.log('Successfully created default config:', data);
      return this.mapDbConfigToTradingConfig(data);
    } catch (error) {
      console.error('Error in createDefaultConfig:', error);
      return null;
    }
  }

  /**
   * Map database config to TradingConfiguration interface
   */
  private mapDbConfigToTradingConfig(dbConfig: any): TradingConfiguration {
    return {
      id: dbConfig.id,
      userId: dbConfig.user_id,
      autoTradeEnabled: dbConfig.auto_trade_enabled,
      tradingMode: dbConfig.trading_mode,
      strategyName: dbConfig.strategy_name,
      strategyParams: dbConfig.strategy_params,
      subscribedInstruments: dbConfig.subscribed_instruments,
      notificationsEnabled: dbConfig.notifications_enabled,
      emailNotifications: dbConfig.email_notifications,
      webhookUrl: dbConfig.webhook_url,
      lastStrategyRun: dbConfig.last_strategy_run ? new Date(dbConfig.last_strategy_run) : undefined,
      dailyTradeCount: dbConfig.daily_trade_count,
      dailyPnl: parseFloat(dbConfig.daily_pnl || '0'),
      lastDailyReset: new Date(dbConfig.last_daily_reset),
      createdAt: new Date(dbConfig.created_at),
      updatedAt: new Date(dbConfig.updated_at)
    };
  }

  /**
   * Check if user can place more trades today
   */
  async canPlaceTrade(userId: string): Promise<boolean> {
    const stats = await this.getTradingStats(userId);
    return stats ? stats.canTrade : false;
  }

  /**
   * Get enabled instruments for trading
   */
  async getEnabledInstruments(userId: string): Promise<Array<{symbol: string; name: string; exchange: string; securityId: number}>> {
    const config = await this.getTradingConfig(userId);
    if (!config) return [];

    return config.subscribedInstruments.filter(instrument => instrument.enabled);
  }
}

// Export singleton instance
export const tradingConfigService = new TradingConfigService();
